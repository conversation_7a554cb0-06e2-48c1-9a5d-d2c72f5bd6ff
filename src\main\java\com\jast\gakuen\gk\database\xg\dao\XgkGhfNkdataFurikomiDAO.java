package com.jast.gakuen.gk.database.xg.dao;

import java.sql.ResultSet;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.jast.gakuen.core.common.database.DbSession;
import com.jast.gakuen.core.common.database.RowValue;
import com.jast.gakuen.core.common.database.RxPreparedStatement;
import com.jast.gakuen.core.common.exception.DbException;
import com.jast.gakuen.core.common.exception.OverTheLimitException;
import com.jast.gakuen.gk.database.gh.base.GhfNkdataFurikomiValue;
import com.jast.gakuen.gk.database.gh.dao.GhfNkdataFurikomiDAO;
import com.jast.gakuen.gk.database.gh.entity.GhfNkdataFurikomiAR;

/**
 * 入金データ＿振込入金通知テーブル(ghf_nkdata_furikomi) DAO拡張クラス<br>
 * 
 * <AUTHOR> System Techniques Co.,Ltd.
 */
public class XgkGhfNkdataFurikomiDAO extends GhfNkdataFurikomiDAO {

	/** SQL文定義 */
	private enum Statement {

		/** SQL_FIND_BY_FURIKOMI_IRAI_CD */
		SQL_FIND_BY_FURIKOMI_IRAI_CD(
				"select * from ghf_nkdata_furikomi "
						+ " where furikomi_irai_cd = ? "
						+ " order by nykn_data_no, nykn_data_sub_no ");

		/** SQL文 */
		private final String sql;

		/**
		 * コンストラクタ
		 * 
		 * @param sql SQL
		 */
		Statement(final String sql) {
			this.sql = sql;
		}
	}

	/** プリペアドステートメントMAP */
	private final Map<Statement, RxPreparedStatement> pstmtMap = new HashMap<>();

	/**
	 * GhfNkdataFurikomiARインスタンス生成
	 *
	 * @param dbs DbSession
	 * @param rv RowValue
	 * @return GhfNkdataFurikomiARインスタンス
	 */
	@Override
	protected GhfNkdataFurikomiAR createActiveRecord(final DbSession dbs, final RowValue rv) {
		return new GhfNkdataFurikomiAR(dbs, (GhfNkdataFurikomiValue) rv);
	}
	
	/**
	 * 振込依頼人コードで振込入金通知データを取得する。<br>
	 *
	 * @param furikomiIraiCd 振込依頼人コード
	 * @return 検索結果
	 * @throws DbException DB例外
	 * @throws OverTheLimitException 処理上限例外
	 */
	public List<GhfNkdataFurikomiAR> findByFurikomiIraiCd(
			final String furikomiIraiCd
			) throws DbException, OverTheLimitException {

		List<GhfNkdataFurikomiAR> result = null;

		try {
			Statement stmt = Statement.SQL_FIND_BY_FURIKOMI_IRAI_CD;
			RxPreparedStatement pstmt = pstmtMap.get(stmt);
			if (pstmt == null) {
				pstmt = prepareQueryStatement(stmt.sql);
				pstmtMap.put(stmt, pstmt);
			}

			int i = 0;
			pstmt.setParam(++i, furikomiIraiCd);

			try (ResultSet rs = pstmt.executeQuery()) {
				result = getActiveRecordList(rs);
			}
		} catch (OverTheLimitException oe) {
			throw oe;
		} catch (Exception e) {
			throw new DbException(e);
		}

		return result;
	}
}
