/*
 * Xgm003Service.java
 *
 * 著作権  ：Copyright Japan System Techniques Co., Ltd. All Rights Reserved.
 * 会社名  ：日本システム技術株式会社
 *
 */
package com.jast.gakuen.gk.xg.service.impl;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.Date;
import java.util.List;

import javax.enterprise.context.RequestScoped;
import javax.faces.model.SelectItem;
import javax.inject.Inject;

import com.jast.gakuen.common.database.gh.dao.GhcPayhBunDAO;
import com.jast.gakuen.common.database.gh.dao.GhcPayhDAO;
import com.jast.gakuen.common.database.gh.dao.GhePaywBunDAO;
import com.jast.gakuen.common.database.gh.dao.GhePaywDAO;

import com.jast.gakuen.common.database.gh.dao.GhzBunKbnDAO;
import com.jast.gakuen.common.database.gh.entity.GhcPayhAR;
import com.jast.gakuen.common.database.gh.entity.GhcPayhBunAR;
import com.jast.gakuen.common.database.gh.entity.GhePaywAR;
import com.jast.gakuen.common.database.gh.entity.GhePaywBunAR;
import com.jast.gakuen.common.database.gh.entity.GhfEnnoAR;
import com.jast.gakuen.common.database.gh.entity.GhzBunKbnAR;
import com.jast.gakuen.common.database.pk.dao.PkaSsnDAO;
import com.jast.gakuen.common.database.pk.dao.PkbGakDAO;
import com.jast.gakuen.common.database.pk.dao.PkbGakHatbnDAO;
import com.jast.gakuen.common.database.pk.dao.PkbGakRekiDAO;
import com.jast.gakuen.common.database.pk.dao.PkbSotDAO;
import com.jast.gakuen.common.database.pk.dao.PkbSotIdoDAO;
import com.jast.gakuen.common.database.pk.entity.PkaSsnAR;
import com.jast.gakuen.common.database.pk.entity.PkbGakAR;
import com.jast.gakuen.common.database.pk.entity.PkbGakHatbnAR;
import com.jast.gakuen.common.database.pk.entity.PkbGakRekiAR;
import com.jast.gakuen.common.database.pk.entity.PkbSotAR;
import com.jast.gakuen.common.database.pk.entity.PkbSotIdoAR;
import com.jast.gakuen.common.gh.constant.code.GakShiKbn;
import com.jast.gakuen.common.gh.constant.code.OutputKbn;
import com.jast.gakuen.common.gh.constant.code.PayStatusKbn;
import com.jast.gakuen.common.pk.constant.code.BunmenKbn;
import com.jast.gakuen.common.pk.constant.code.IdoApplyKekkaKbn;
import com.jast.gakuen.common.pk.constant.code.IdoShutgakKbn;
import com.jast.gakuen.core.common.CodeManager;
import com.jast.gakuen.core.common.SessionInfo;
import com.jast.gakuen.core.common.constant.code.PrdKbn;
import com.jast.gakuen.core.common.database.DbSession;
import com.jast.gakuen.core.common.dto.CodeDTO;
import com.jast.gakuen.core.common.exception.GakuenException;
import com.jast.gakuen.core.common.util.Message;
import com.jast.gakuen.core.common.util.UtilLocalization;
import com.jast.gakuen.core.common.util.UtilStr;
import com.jast.gakuen.gk.database.gh.dao.GhaNenDAO;
import com.jast.gakuen.gk.database.gh.dao.GhbGakDAO;
import com.jast.gakuen.gk.database.gh.dao.GhbSotDAO;
import com.jast.gakuen.gk.database.gh.dao.GhzBunMenDAO;
import com.jast.gakuen.gk.database.gh.entity.GhaNenAR;
import com.jast.gakuen.gk.database.gh.entity.GhbGakAR;
import com.jast.gakuen.gk.database.gh.entity.GhbSotAR;
import com.jast.gakuen.gk.database.gh.entity.GhzBunMenAR;
import com.jast.gakuen.gk.database.xg.dao.XgkGhcPayhDAO;
import com.jast.gakuen.gk.database.xg.dao.XgxMeiKanriKmkDAO;
import com.jast.gakuen.gk.database.xg.entity.XgxMeiKanriKmkAR;
import com.jast.gakuen.gk.gh.constant.Ghd008OrderConst;
import com.jast.gakuen.gk.gh.dto.Ghd008DTO01;
import com.jast.gakuen.gk.gh.dto.Ghd008DTO03;
import com.jast.gakuen.gk.gh.dto.Ghd008DTO04;
import com.jast.gakuen.gk.gh.dto.Ghd008DTO05;
import com.jast.gakuen.gk.gh.dto.Ghd008DTO06;
import com.jast.gakuen.gk.gh.dto.Ghd008DTO12;
import com.jast.gakuen.gk.pk.business.PkbGakAuthLogic;
import com.jast.gakuen.gk.pk.business.PkbGakCheckLogic;
import com.jast.gakuen.gk.pk.dto.PkbGakAuthConditionDTO01;
import com.jast.gakuen.gk.pk.dto.PkbGakCheckResultDTO;
import com.jast.gakuen.gk.xg.constant.XgmConst;
import com.jast.gakuen.gk.xg.dto.Xgm003ConditionDTO02;
import com.jast.gakuen.gk.xg.dto.Xgm003ConditionDTO04;
import com.jast.gakuen.gk.xg.dto.Xgm003DTO02;
import com.jast.gakuen.gk.xg.service.IXgm003Service;

/**
 * 学生納付金通知書サービス
 *
 * <AUTHOR> System Techniques Co.,Ltd.
 */
@RequestScoped
public class Xgm003Service implements IXgm003Service {

	/**
	 * DbSession
	 */
	@Inject
	protected DbSession dbs;

	@Override
	public List<Ghd008DTO01> getOrderList() throws Exception {
		List<Ghd008DTO01> ret = new ArrayList<>();
		for (Ghd008OrderConst order : Ghd008OrderConst.values()) {
			Ghd008DTO01 dto = null;
			switch (order) {
			case gaknen:
				dto = new Ghd008DTO01(order.getKey(), order.getDispLabel());
				dto.setPriority(1);
				break;
			case semester:
				dto = new Ghd008DTO01(order.getKey(), order.getDispLabel());
				dto.setPriority(2);
				break;
			case sgks:
				dto = new Ghd008DTO01(order.getKey(), order.getDispLabel());
				dto.setPriority(3);
				break;
			case postCd:
				dto = new Ghd008DTO01(order.getKey(), order.getDispLabel());
				dto.setPriority(4);
				break;
			default:
				dto = new Ghd008DTO01(order.getKey(), order.getDispLabel());
				dto.setPriority(1);
				break;
			}

			ret.add(dto);
		}
		return ret;
	}

	@Override
	public List<Xgm003DTO02> getPayHList(final Xgm003ConditionDTO02 condition) throws Exception {

		List<Xgm003DTO02> rtnList = new ArrayList<>();
		XgkGhcPayhDAO xgkGhcPayhDAO = dbs.getDao(XgkGhcPayhDAO.class);
		GhcPayhBunDAO ghcPayhBunDAO = dbs.getDao(GhcPayhBunDAO.class);

		// 第1ステップ：XGM_PAY表検索（業務コード絞り込み）+ 第2ステップ：ghc_PAYH表検索（納付金配当情報）
		List<GhcPayhAR> ghcPayhARList = xgkGhcPayhDAO.findByNendoGyomuCdPayCdShubetsu(
				condition.getWariateNendo(),    // 学費年度
				condition.getGyomucd(),         // 業務コード
				condition.getNoufuKinShubetsu() // 納付金種別（A/B/C）
		);

		// 第3ステップ：ghc_PAYH_BUN表検索（分納詳細情報）
		for (GhcPayhAR ghcPayhAR : ghcPayhARList) {
			// 各ghc_payh記録に対応する分納情報を検索
			List<GhcPayhBunAR> ghcPayhBunARList = ghcPayhBunDAO.findByNendoPayPatternBunnoKbn(
					ghcPayhAR.getNendo(),
					ghcPayhAR.getPayCd(),
					ghcPayhAR.getPatternCd(),
					ghcPayhAR.getBunnoKbnCd());

			for (GhcPayhBunAR ghcPayhBunAR : ghcPayhBunARList) {
				Xgm003DTO02 dto = new Xgm003DTO02();
				dto.setNendo(ghcPayhBunAR.getNendo());
				dto.setPayCd(ghcPayhBunAR.getPayCd());
				dto.setPatternCd(ghcPayhBunAR.getPatternCd());
				dto.setBunnoKbnCd(ghcPayhBunAR.getBunnoKbnCd());

				// 分納区分 取得
				GhzBunKbnAR bunKbnAR = getGhzBunKbnAR(ghcPayhBunAR.getBunnoKbnCd());
				if (bunKbnAR != null) {
					dto.setBunnoKbnName(bunKbnAR.getBunnoKbnName());
				}

				dto.setBunkatsuNo(ghcPayhBunAR.getBunkatsuNo());
				dto.setPayName(ghcPayhAR.getPayName());

				rtnList.add(dto);
			}
		}

		// rtnList.sort(Comparator.comparing(Xgm003DTO02::getNendo)
		// 		.thenComparing(Xgm003DTO02::getPayCd)
		// 		.thenComparing(Xgm003DTO02::getPatternCd)
		// 		.thenComparing(Xgm003DTO02::getBunnoKbnCd)
		// 		.thenComparing(Xgm003DTO02::getBunkatsuNo));

		return rtnList;
	}

	@Override
	public List<Xgm003DTO02> getPayWList(final Xgm003ConditionDTO04 condition) throws Exception {
		List<Xgm003DTO02> rtnList = new ArrayList<>();

		// 振込依頼人コードが指定されている場合の検証
		if (UtilStr.isNotEmpty(condition.getFurikomiIraiCd())) {
			// TODO: 振込依頼人コード検証ロジックを実装
			// GhgFurikomiDAO等を使用して検証
		}

		// 学籍番号から管理番号を取得
		Long kanriNo = null;
		if (UtilStr.isNotEmpty(condition.getGaksekiCd())) {
			GhbGakDAO ghbGakDAO = dbs.getDao(GhbGakDAO.class);
			List<GhbGakAR> ghbGakARList = ghbGakDAO.findByGaksekiCd(condition.getGaksekiCd());
			if (!ghbGakARList.isEmpty()) {
				kanriNo = ghbGakARList.get(0).getKanriNo();
			}
		}

		if (kanriNo == null) {
			return rtnList; // 管理番号が取得できない場合は空リストを返す
		}

		GhePaywBunDAO ghePaywBunDAO = dbs.getDao(GhePaywBunDAO.class);
		GhePaywDAO ghePaywDAO = dbs.getDao(GhePaywDAO.class);
		GhcPayhDAO ghcPayhDAO = dbs.getDao(GhcPayhDAO.class);

		List<GhePaywBunAR> ghePaywBunARList = ghePaywBunDAO.findByKanriNo(kanriNo);
		for (GhePaywBunAR ghePaywBunAR : ghePaywBunARList) {

			// 納付金割当を取得
			GhePaywAR ghePaywAR = ghePaywDAO.findByPrimaryKey(
					ghePaywBunAR.getNendo(),
					ghePaywBunAR.getKanriNo(),
					ghePaywBunAR.getPayCd(),
					ghePaywBunAR.getPatternCd(),
					ghePaywBunAR.getBunnoKbnCd());
			if (ghePaywAR == null) {
				continue;
			}

			// 納付金割当.納付金状態区分が[1：通常]以外は読み飛ばす
			if (!PayStatusKbn.Tujo.getCode().equals(ghePaywAR.getPayStatusKbn())) {
				continue;
			}

			// 納付金割当.学生志願者区分が[1：学生]以外は読み飛ばす
			if (!GakShiKbn.Gaksei.getCode().equals(ghePaywAR.getGakShiKbn())) {
				continue;
			}

			// 納付金配当を取得
			GhcPayhAR ghcPayhAR = ghcPayhDAO.findByPrimaryKey(
					ghePaywBunAR.getNendo(),
					ghePaywBunAR.getPayCd(),
					ghePaywBunAR.getPatternCd(),
					ghePaywBunAR.getBunnoKbnCd());
			if (ghcPayhAR == null) {
				continue;
			}

			Xgm003DTO02 dto = new Xgm003DTO02();
			dto.setNendo(ghePaywBunAR.getNendo());
			dto.setPayCd(ghePaywBunAR.getPayCd());
			dto.setPatternCd(ghePaywBunAR.getPatternCd());
			dto.setBunnoKbnCd(ghePaywBunAR.getBunnoKbnCd());

			// 分納区分 取得
			GhzBunKbnAR bunKbnAR = getGhzBunKbnAR(ghePaywBunAR.getBunnoKbnCd());
			if (bunKbnAR != null) {
				dto.setBunnoKbnName(bunKbnAR.getBunnoKbnName());
			}

			dto.setBunkatsuNo(ghePaywBunAR.getBunkatsuNo());
			dto.setPayName(ghcPayhAR.getPayName());
			dto.setPayLimitDate(ghePaywBunAR.getPayLimitDate());

			rtnList.add(dto);
		}

		rtnList.sort(Comparator.comparing(Xgm003DTO02::getNendo)
				.thenComparing(Xgm003DTO02::getPayCd)
				.thenComparing(Xgm003DTO02::getPatternCd)
				.thenComparing(Xgm003DTO02::getBunnoKbnCd)
				.thenComparing(Xgm003DTO02::getBunkatsuNo));
		return rtnList;
	}

	@Override
	public Ghd008DTO05 checkGaksekiCd(final Ghd008DTO05 condition) throws Exception {

		Ghd008DTO05 rtnDto = new Ghd008DTO05();

		Ghd008DTO05 gakseiInfoDto = this.getGakseiInfo(condition);
		if (gakseiInfoDto.getErrorMessage() != null) {
			throw new GakuenException(gakseiInfoDto.getErrorMessage());
		}

		rtnDto.setProductKbn(gakseiInfoDto.getProductKbn());
		rtnDto.setGakSotKbn(gakseiInfoDto.getGakSotKbn());
		rtnDto.setKanriNo(gakseiInfoDto.getKanriNo());
		rtnDto.setGakseiName(gakseiInfoDto.getGakseiName());
		rtnDto.setErrorMessage(gakseiInfoDto.getErrorMessage());

		return rtnDto;
	}

	@Override
	public Ghd008DTO05 getGakseiName(final Ghd008DTO05 condition) throws Exception {

		Ghd008DTO05 rtnDto = new Ghd008DTO05();

		Ghd008DTO05 gakseiInfoDto = this.getGakseiInfo(condition);
		rtnDto.setGakseiName(gakseiInfoDto.getGakseiName());

		return rtnDto;
	}

	/**
	 * 学生情報取得
	 *
	 * @param condition 学生情報パラメータ
	 * @return 学生情報DTO
	 * @throws Exception 例外
	 */
	protected Ghd008DTO05 getGakseiInfo(final Ghd008DTO05 condition) throws Exception {

		SessionInfo sessionInfo = SessionInfo.getSessionInfo();
		PkbGakAuthLogic gakAuth = new PkbGakAuthLogic(
				dbs,
				condition.getSkijunDate(),
				sessionInfo.getLoginUser().getUserId());

		Ghd008DTO05 rtnDto = new Ghd008DTO05();

		// ★★★★★★★★★★★★★★★★★★★★★★★★★★★★★★★★★★★★★
		// 以下の優先順で学生の取得を行う
		// ①教務在学生、②教務出学生、③学費学生、④学費出学生
		// ★★★★★★★★★★★★★★★★★★★★★★★★★★★★★★★★★★★★★

		// *******************************************
		// ①教務在学生であるか確認
		// *******************************************
		// 学籍番号発番管理情報を主キーで検索する。
		PkbGakHatbnDAO pkbGakHatbnDAO = dbs.getDao(PkbGakHatbnDAO.class);
		PkbGakHatbnAR pkbGakHatbnAR = pkbGakHatbnDAO.findByPrimaryKey(condition.getGaksekiCd());

		if (pkbGakHatbnAR != null) {
			// 学籍台帳情報を取得する。
			PkbGakDAO pkbGakDAO = dbs.getDao(PkbGakDAO.class);
			PkbGakAR pkbGakAR = pkbGakDAO.findByPrimaryKey(pkbGakHatbnAR.getKanriNo());
			if (pkbGakAR != null) {

				// 権限チェック
				PkbGakAuthConditionDTO01 gakAuthDTO = new PkbGakAuthConditionDTO01();
				gakAuthDTO.setKanriNo(pkbGakHatbnAR.getKanriNo());
				if (!gakAuth.hasGakAuth(gakAuthDTO)) {
					// 権限のない学生が指定されました。
					rtnDto.setErrorMessage(new Message(PrdKbn.SY.getCode(), Message.TypeCode.E, 194));
					return rtnDto;
				}

				// 学籍台帳情報が取得できた場合。
				rtnDto.setProductKbn("PK");
				rtnDto.setGakSotKbn("GAK");
				rtnDto.setKanriNo(pkbGakAR.getKanriNo());
				rtnDto.setGakseiName(pkbGakAR.getGakseiName());

				// --------------------------------------------------------------
				// 転科チェック
				// --------------------------------------------------------------
				// 指定した学籍番号、所属基準日から有効な学籍履歴を取得する。
				PkbGakRekiDAO pkbGakRekiDAO = dbs.getDao(PkbGakRekiDAO.class);
				PkbGakRekiAR pkbGakRekiAR = pkbGakRekiDAO.findCurrentByGaksekiCdSKijunDate(condition.getGaksekiCd(), condition.getSkijunDate());
				if (pkbGakRekiAR == null) {
					// 学籍履歴が存在しない場合、転科前学籍番号であるか確認
					PkbGakCheckLogic checkLogic = new PkbGakCheckLogic(dbs, condition.getSkijunDate(), condition.getGaksekiCd());
					PkbGakCheckResultDTO checkTenkaMaeGaksekiCdResult = checkLogic.checkTenkaMaeGaksekiCd(true);
					if (checkTenkaMaeGaksekiCdResult.getMessage() != null) {
						// 「転科前の学籍番号です」
						rtnDto.setErrorMessage(new Message(PrdKbn.PK.getCode(), Message.TypeCode.E, 45));
					} else {
						// 「有効な学籍番号ではありません。」
						rtnDto.setErrorMessage(
								new Message(PrdKbn.PK.getCode(), Message.TypeCode.E, 141, UtilLocalization.getItemValue("common.gaksekiCd.0.label")));
					}
				}

				return rtnDto;
			}
		}

		// *******************************************
		// ②教務卒業生であるか確認
		// *******************************************
		// 卒業生異動情報リストを取得する。
		PkbSotIdoDAO pkbSotIdoDAO = dbs.getDao(PkbSotIdoDAO.class);
		List<PkbSotIdoAR> pkbSotIdoARList = pkbSotIdoDAO.findByGaksekiCd(condition.getGaksekiCd());
		// 出学年度、出学学期ＮＯの降順でソート。
		pkbSotIdoARList.sort(Comparator.comparing(PkbSotIdoAR::getIdoShutgakNendo).reversed()
				.thenComparing(Comparator.comparing(PkbSotIdoAR::getIdoShutgakGakkiNo).reversed()));
		for (PkbSotIdoAR sotIdoAR : pkbSotIdoARList) {
			if (!IdoShutgakKbn.Shutsugak.getCode().equals(sotIdoAR.getIdoShutgakKbn())
					|| !IdoApplyKekkaKbn.Kyoka.getCode().equals(sotIdoAR.getIdoShutgakApplyKekkaKbn())) {
				// 「出学：許可」でなければ対象外
				continue;
			}
			if (sotIdoAR.getIdoShutgakEndDate() != null) {
				// 「卒業生異動.異動出学終了日」がnullでなければ対象外
				continue;
			}

			// 卒業生台帳テーブルを取得する
			PkbSotDAO sotDao = dbs.getDao(PkbSotDAO.class);
			PkbSotAR sotAR = sotDao.findByPrimaryKey(sotIdoAR.getKanriNo());
			if (sotAR == null || !sotAR.isYukoFlg()) {
				// 「有効」でなければ対象外
				continue;
			}

			// 権限チェック
			PkbGakAuthConditionDTO01 sotAuthDTO = new PkbGakAuthConditionDTO01();
			sotAuthDTO.setKanriNo(sotAR.getKanriNo());
			if (!gakAuth.hasSotAuth(sotAuthDTO)) {
				// 権限のない学生が指定されました。
				rtnDto.setErrorMessage(new Message(PrdKbn.SY.getCode(), Message.TypeCode.E, 194));
				return rtnDto;
			}

			rtnDto.setProductKbn("PK");
			rtnDto.setGakSotKbn("SOT");
			rtnDto.setKanriNo(sotAR.getKanriNo());
			rtnDto.setGakseiName(sotAR.getGakseiName());

			GhePaywBunDAO ghePaywBunDAO = dbs.getDao(GhePaywBunDAO.class);
			List<GhePaywBunAR> ghePaywBunARList = ghePaywBunDAO.findByKanriNo(sotAR.getKanriNo());
			if (ghePaywBunARList.isEmpty()) {
				// 納付金割当が無い卒業生です。
				rtnDto.setErrorMessage(new Message(PrdKbn.GH.getCode(), Message.TypeCode.E, 14));
			}

			return rtnDto;
		}

		// *******************************************
		// ③学費学生であるか確認
		// *******************************************
		// 学費学籍台帳を取得
		GhbGakDAO gakDao = dbs.getDao(GhbGakDAO.class);
		List<GhbGakAR> ghbGakARList = gakDao.findByGakSeiName(condition.getGaksekiCd(), null);
		for (GhbGakAR ar : ghbGakARList) {
			rtnDto.setProductKbn("GH");
			rtnDto.setGakSotKbn("GAK");
			rtnDto.setKanriNo(ar.getKanriNo());
			rtnDto.setGakseiName(ar.getGakseiName());
			return rtnDto;
		}

		// *******************************************
		// ④学費出学生であるか確認
		// *******************************************
		// 学費卒業生台帳を取得
		GhbSotDAO ghbSotDAO = dbs.getDao(GhbSotDAO.class);
		List<GhbSotAR> ghbSotARList = ghbSotDAO.findByGakSeiName(condition.getGaksekiCd(), null);
		for (GhbSotAR ar : ghbSotARList) {
			rtnDto.setProductKbn("GH");
			rtnDto.setGakSotKbn("SOT");
			rtnDto.setKanriNo(ar.getKanriNo());
			rtnDto.setGakseiName(ar.getGakseiName());
			// 納付金割当が無い卒業生です。
			rtnDto.setErrorMessage(new Message(PrdKbn.GH.getCode(), Message.TypeCode.E, 14));
			return rtnDto;
		}

		// 指定した{0}は存在しません。
		rtnDto.setErrorMessage(new Message(PrdKbn.SY.getCode(), Message.TypeCode.E, 199, UtilLocalization.getItemValue("pkbGakReki.gaksekiCd.0.label")));
		return rtnDto;
	}

	@Override
	public Ghd008DTO06 getShshnkName(final Ghd008DTO06 condition) throws Exception {

		Ghd008DTO06 rtnDto = new Ghd008DTO06();

		// 出身校取得
		PkaSsnDAO dao = dbs.getDao(PkaSsnDAO.class);
		PkaSsnAR ar = dao.findByPrimaryKey(condition.getShshnkCd());
		if (ar == null) {
			return rtnDto;
		}

		rtnDto.setShshnkName(ar.getShshnkName());
		return rtnDto;
	}

	@Override
	public Ghd008DTO12 getCurrentNendo() throws Exception {
		Ghd008DTO12 rtnDto = new Ghd008DTO12();
		GhaNenDAO dao = dbs.getDao(GhaNenDAO.class);
		List<GhaNenAR> arList = dao.findAll();
		// 先頭データの学費業務年度.年度を返す。
		if (!arList.isEmpty()) {
			rtnDto.setNendo(arList.get(0).getNendo());
		}

		return rtnDto;
	}

	@Override
	public List<SelectItem> getBunmenItems() throws Exception {
		String[] bunmenKbnArray = new String[] {
				BunmenKbn.Gh2rnou.getCode(),
				BunmenKbn.Gh3rnou.getCode(),
				BunmenKbn.Ghsnou.getCode(),
				BunmenKbn.Ghitnou.getCode(),
				BunmenKbn.Ghsou.getCode(),
				BunmenKbn.Ghtok.getCode()
		};
		CodeManager codeManager = new CodeManager();
		CodeDTO codeDto = codeManager.getCode("PK0093", "0", "0");

		List<SelectItem> comBox = new ArrayList<>();

		// 学費文面
		GhzBunMenDAO ghzBunMenDAO = dbs.getDao(GhzBunMenDAO.class);
		for (String bunmenKbn : bunmenKbnArray) {
			List<GhzBunMenAR> ghzBunMenARList = ghzBunMenDAO.findByBummenKbn(bunmenKbn);

			ghzBunMenARList.sort(Comparator.comparing(GhzBunMenAR::getReportSubNo));

			for (GhzBunMenAR ar : ghzBunMenARList) {
				comBox.add(new SelectItem(
						ar.getBummenKbn() + "," + String.valueOf(ar.getReportSubNo()),
						UtilLocalization.getItemValue("common.kakko.daiOpen.1.label")
								+ UtilStr.cnvNull(codeDto.getLabel(bunmenKbn))
								+ UtilLocalization.getItemValue("common.kakko.daiClose.1.label")
								+ UtilStr.cnvNull(ar.getTitle())));
			}
		}
		// 結果を呼び出し元に返却。
		return comBox;
	}

	/**
	 * 分納区分情報取得
	 *
	 * @param bunnoKbnCd 分納区分コード
	 * @return GhzBunKbnAR:分納区分
	 * @throws Exception 例外
	 */
	private GhzBunKbnAR getGhzBunKbnAR(final int bunnoKbnCd) throws Exception {
		// 分納区分
		GhzBunKbnDAO dao = dbs.getDao(GhzBunKbnDAO.class);
		return dao.findByPrimaryKey(bunnoKbnCd);
	}

	@Override
	public Integer getGakuhiNoufuKigenRange() throws Exception {
		XgxMeiKanriKmkDAO xgxMeiKanriKmkDAO = dbs.getDao(XgxMeiKanriKmkDAO.class);
		List<XgxMeiKanriKmkAR> xgxMeiKanriKmkARList = xgxMeiKanriKmkDAO.findBySbtGyomu(XgmConst.M06.getCode(), "M");
		if (!xgxMeiKanriKmkARList.isEmpty()) {
			XgxMeiKanriKmkAR xgxMeiKanriKmkAR = xgxMeiKanriKmkARList.get(0);
			if (xgxMeiKanriKmkAR.getKmkName1() != null) {
				return Integer.valueOf(xgxMeiKanriKmkAR.getKmkName1());
			}
		}
		return 12; // デフォルト値
	}
}
