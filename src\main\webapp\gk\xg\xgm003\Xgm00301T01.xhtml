<?xml version='1.0' encoding='UTF-8' ?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.1//EN" "http://www.w3.org/TR/xhtml11/DTD/xhtml11.dtd">
<ui:composition xmlns="http://www.w3.org/1999/xhtml"
                xmlns:h="http://xmlns.jcp.org/jsf/html"
                xmlns:f="http://xmlns.jcp.org/jsf/core"
                xmlns:ui="http://xmlns.jcp.org/jsf/facelets"
                xmlns:p="http://primefaces.org/ui"
                xmlns:c="http://xmlns.jcp.org/jsp/jstl/core"
                xmlns:rx="http://xmlns.jcp.org/jsf/composite/component">

    <!-- 出身校検索画面選択結果取得 -->
    <p:remoteCommand name="t1_receiveShshnk" actionListener="#{xgm00301T01Bean.doGetShshnkCd()}" process="@this" update="t1_shshnkCd t1_shshnkName"/>
    <!-- 自由設定出力条件設定画面結果取得 -->
    <p:remoteCommand name="t1_receivePkGkfrCondition" actionListener="#{xgm00301T01Bean.doReceivePkGkfrCondition}" process="@this" update="t1_frArea" />
    <p:remoteCommand name="t1_receiveGhGkfrCondition" actionListener="#{xgm00301T01Bean.doReceiveGhGkfrCondition}" process="@this" update="t1_frArea" />
    
    <!--コード-->
    <c:set var="ryugakseiFlg" value="#{code.getCode('GH0003','1')}" />
    <c:set var="ryugakseiKbn" value="#{code.getCode('PK0010')}" />
    <c:set var="shogaisyaFlg" value="#{code.getCode('GH0004','1')}" />
    <c:set var="shakaijinFlg" value="#{code.getCode('GH0036','1')}" />
    <c:set var="idoSin" value="#{code.getCode('PK0015')}"/>
    <c:set var="idoSbt" value="#{code.getCode('PK0014')}" />
    <c:set var="noufuKinShubetsu" value="#{code.getCode('XG0001')}" />
    <section>

        <p:outputPanel id="t1_paySearchCondition">
            <dl class="inputArea">
                <!-- 割当年度 -->
                <div class="itemTable">
                    <dt class="hissu">
                        <p:outputLabel for="funcForm:tabArea:t1_wariateNendo:number" value="#{item['xgm003.wariateNendo.0.label']}" />
                        <rx:gkSizeLabel />
                    </dt>
                    <dd>
                        <rx:inputNumber id="t1_wariateNendo" widgetVar="nendo" value="#{xgm00301T01Bean.condition.wariateNendo}" styleClass="inputHalfSize4" >
                            <p:clientValidator event="blur" />
                        </rx:inputNumber>
                        <p:message for="funcForm:tabArea:t1_wariateNendo:number" display="tooltip" />
                    </dd>
                </div>
                <!--納付金種別-->
                <div class="itemTable">
                    <dt>
                        <p:outputLabel value="#{item['xgm003.noufuKinShubetsu.0.label']}" />
                    </dt>
                    <dd>
                        <p:selectManyCheckbox id="t1_noufuKinShubetsu" value="#{xgm00301T01Bean.condition.noufuKinShubetsu}">
                            <f:selectItems value="#{noufuKinShubetsu.itemValues}" var="itemVal"
                                           itemValue="#{itemVal}" itemLabel="#{noufuKinShubetsu.getShortLabel(itemVal)}" />
                        </p:selectManyCheckbox>
                    </dd>
                </div>
                <!-- 業務コード -->
                <div class="itemTable">
                    <dt>
                        <p:outputLabel for="t1_gyomucd" value="#{item['xgm003.gyomucd.0.label']}" />
                        <rx:gkSizeLabel />
                    </dt>
                    <dd>
                        <p:selectOneMenu id="t1_gyomucd" value="#{xgm00301T01Bean.condition.gyomucd}">
                            <f:selectItems value="#{ghzSelectItem.getGyoumuItems('M01', 'M')}" />
                        </p:selectOneMenu>
                        <p:message for="t1_gyomucd" display="tooltip" />
                    </dd>
                </div>
            </dl>
        </p:outputPanel>

        <!-- ボタンエリア -->
        <p:outputPanel styleClass="btnAreaFuncBottom" >
            <!-- 検索 -->
            <p:commandButton id="t1_paySearch" value="#{btn['button.001']}" actionListener="#{xgm00301Bean.doSearchPayHList(1)}"
                             process="@this funcForm:mainCondition t1_paySearchCondition"
                             update="@this t1_paySearchCondition funcForm:tabArea:t1_payList funcForm:tabArea:t1_paySearch funcForm:tabArea:t1_payClear"
                             styleClass="btnSearch letterSpaceHalf" />
            <!-- クリア -->
            <p:commandButton id="t1_payClear" value="#{btn['button.008']}" actionListener="#{xgm00301T01Bean.doClear}"
                             process="@this"
                             update="@this funcForm:tabArea:t1_paySearchCondition funcForm:tabArea:t1_payList funcForm:tabArea:t1_paySearch funcForm:tabArea:t1_payClear"
                             styleClass="btnClear"/>
        </p:outputPanel>
            
        <p:outputPanel id="t1_mainCondition">
            <dl>
                <div>
                    <!-- 納付金リスト -->
                    <p:dataTable id="#{xgm00301Bean.tblId[0]}"
                                 rows="#{xgm00301Bean.tblRows[0]}"
                                 first="#{xgm00301Bean.tblFirst[0]}"
                                 var="data" value="#{xgm00301T01Bean.payList}"
                                 selection="#{xgm00301T01Bean.selectedPayList}" rowKey="#{data.hashCode()}"
                                 widgetVar="dataTable" paginator="true" resizableColumns="false" scrollHeight="140"
                                 emptyMessage="#{xgm00301T01Bean.searched?msgSY['E_SY_00090']:''}">

                        <!--ページャ保存-->
                        <p:ajax event="page" listener="#{xgm00301Bean.onPage}" />

                        <!-- チェックボックス -->
                        <p:column selectionMode="multiple" class="colSizeCheckbox alignCenter" />

                        <!-- 割当年度 -->
                        <p:column id="t1_colNendo" headerText="#{item['ghePaywBun.nendo.0.label']}"
                                  sortBy="#{data.nendo}" styleClass="colSize6 alignLeft">
                            <h:outputText value="#{data.nendo}" />
                        </p:column>

                        <!-- 納付金コード -->
                        <p:column id="t1_colpayCd" headerText="#{item['ghePaywBun.payCd.0.label']}"
                                  sortBy="#{data.payCd}" styleClass="colSize8 alignLeft">
                            <h:outputText value="#{data.payCd}" />
                        </p:column>

                        <!-- パターンコード -->
                        <p:column id="t1_colPatternCd" headerText="#{item['ghePaywBun.patternCd.0.shortLabel']}"
                                  sortBy="#{data.patternCd}"
                                  styleClass="colSize9 alignLeft">
                            <h:outputText value="#{data.patternCd}" />
                        </p:column>

                        <!-- 分納区分コード -->
                        <p:column id="t1_colBunnoKbnCd" headerText="#{item['ghePaywBun.bunnoKbnCd.0.shortLabel']}"
                                  sortBy="#{data.bunnoKbnCd}"
                                  styleClass="colSize9 alignLeft">
                            <h:outputText value="#{xgm00301Bean.doJoinCodeName(data.bunnoKbnCd,data.bunnoKbnName)}" />
                        </p:column>

                        <!-- 納付金名称 -->
                        <p:column id="t1_colPayName" headerText="#{item['ghcPayh.payName.0.label']}"
                                  sortBy="#{data.payName}"
                                  styleClass="colSizeInitial alignLeft">
                            <h:outputText value="#{data.payName}" />
                        </p:column>

                        <!-- 分割NO -->
                        <p:column id="t1_colBunkatsuNo" headerText="#{item['ghePaywBun.bunkatsuNo.0.label']}"
                                  sortBy="#{data.bunkatsuNo}"
                                  styleClass="colSize6 alignRight">
                            <h:outputText value="#{data.bunkatsuNo}" />
                        </p:column>
                    </p:dataTable>
                </div>
            </dl>
            
            <p:separator />
            
            <dl class="inputArea">
                <div class="itemTable">
                <!-- 発行対象状況区分 -->
                    <dt>
                        <p:outputLabel value="#{item['ghd008.hakkoTgtKbn.0.label']}" />
                    </dt>
                    <dd>
                            <p:selectBooleanCheckbox id="t1_hakkoTgtZengakuMino" itemLabel="#{hakkoTgt.getLabel(1)}"
                                                     value="#{xgm00301T01Bean.condition.hakkoTgtZengakuMino}" /> 
                            <p:selectBooleanCheckbox id="t1_hakkoTgtIchibuMino" itemLabel="#{hakkoTgt.getLabel(2)}" 
                                                     value="#{xgm00301T01Bean.condition.hakkoTgtIchibuMino}" /> 
                    </dd>
                </div>
            </dl>
            <p:fieldset legend="#{item['ghd008.gakuseiCond.0.label']}" toggleable="true" collapsed="true">
                <p:outputPanel id="t1_gakuseiCond">
                    <dl class="inputArea">
                        <div class="itemTable">
                            <dt>
                                <!-- 管轄-->
                                <p:outputLabel value="#{item['common.kankatsu.0.label']}"/> 
                            </dt>
                            <dd>
                                 <p:selectManyCheckbox value="#{xgm00301T01Bean.condition.manageStuList}">
                                    <f:selectItems value="#{manage.itemValues}" var="itemVal" itemValue="#{itemVal}" itemLabel="#{manage.getLabel(itemVal)}" />
                                </p:selectManyCheckbox>
                            </dd>
                        </div>
                        <div class="itemTable">
                            <!-- 入学年度 -->
                            <dt>
                                <p:outputLabel for="t1_nyugakNendo:number" value="#{item['common.nyugakNendo.0.label']}" />
                                <rx:gkSizeLabel />
                            </dt>
                            <dd>
                                <rx:inputNumber id="t1_nyugakNendo" widgetVar="t1_nyugakNendo" value="#{xgm00301T01Bean.condition.nyugakNendo}"
                                                styleClass="inputHalfSize4" >
                                    <p:clientValidator event="blur" />
                                </rx:inputNumber>
                                <p:message for="funcForm:tabArea:t1_nyugakNendo:number" display="tooltip" />
                            </dd>
                            <!-- 入学学期ＮＯ -->
                            <dt>
                                <p:outputLabel for="t1_gakkiNo:number" value="#{item['common.nyugakGakkiNo.0.label']}" />
                                <rx:gkSizeLabel />
                            </dt>
                            <dd>
                                <rx:inputNumber id="t1_gakkiNo" widgetVar="gakkiNo" value="#{xgm00301T01Bean.condition.nyugakGakkiNo}" styleClass="inputHalfSize2" >
                                    <p:clientValidator event="blur" />
                                </rx:inputNumber>
                                <p:message for="funcForm:tabArea:t1_gakkiNo:number" display="tooltip" />
                            </dd>
                        </div>
                        <p:outputPanel id="t1_bushoSgksArea">
                            <div class="itemTable">
                                <!--学生管理部署-->
                                <dt><p:outputLabel value="#{item['common.pkbGakBusho.0.label']}" /></dt>
                                <dd>
                                    <p:selectOneMenu id="t1_gakBusho" value="#{xgm00301T01Bean.condition.gbushoCd}"
                                                      >
                                        <!--選択してください-->
                                        <f:selectItem itemLabel="#{item['common.select.sentaku.0.label']}" />
                                        <f:selectItems value="#{pkzSelectItem.getGakBshoItems(true)}" />
                                    </p:selectOneMenu>
                                </dd>
                            </div>
                            <div class="itemTable">
                                <!--所属学科組織-->
                                <dt><p:outputLabel value="#{item['common.sgks.0.label']}" /></dt>
                                <dd>
                                    <p:selectOneMenu id="t1_sgksCd" value="#{xgm00301T01Bean.condition.sgksCd}"
                                                     >
                                        <!--選択してください-->
                                        <f:selectItem itemLabel="#{item['common.select.sentaku.0.label']}" />
                                        <f:selectItems value="#{pkzSelectItem.getSgksItems(true)}" />
                                    </p:selectOneMenu>
                                </dd>
                            </div>
                        </p:outputPanel>
                        <p:outputPanel id="t1_ghSgksArea" >
                            <div class="itemTable">
                                <!--学費所属学科組織-->
                                <dt><p:outputLabel value="#{item['common.ghbSgks.0.label']}" /></dt>
                                <dd>
                                    <p:selectOneMenu id="t1_ghSgksCd" value="#{xgm00301T01Bean.condition.ghSgksCd}"
                                                     >
                                        <!--選択してください-->
                                        <f:selectItem itemLabel="#{item['common.select.sentaku.0.label']}" />
                                        <f:selectItems value="#{ghzSelectItem.getSgksItems()}" />
                                    </p:selectOneMenu>
                                </dd>
                            </div>
                        </p:outputPanel>
                        <p:outputPanel id="t1_cgksArea" >
                            <!-- みなし入学年度 -->
                            <div class="itemTable">
                                <dt>
                                    <p:outputLabel for="t1_nyugakNendoDeemed:number" value="#{item['pkbGakIdo.nyugakNendoDeemed.0.label']}" />
                                    <rx:gkSizeLabel />
                                </dt>
                                <dd>
                                    <rx:inputNumber id="t1_nyugakNendoDeemed" widgetVar="nyugakNendoDeemed" value="#{xgm00301T01Bean.condition.nyugakNendoDeemed}"
                                                    styleClass="inputHalfSize4" 
                                                    >
                                        <p:clientValidator event="blur" />
                                    </rx:inputNumber>
                                    <p:message for="t1_nyugakNendoDeemed:number" display="tooltip" />
                                </dd>
                                <!-- みなし入学学期ＮＯ -->
                                <dt>
                                    <p:outputLabel for="t1_nyugakGakkiNoDeemed:number" value="#{item['pkbGakIdo.nyugakGakkiNoDeemed.0.label']}" />
                                    <rx:gkSizeLabel />
                                </dt>
                                <dd>
                                    <rx:inputNumber id="t1_nyugakGakkiNoDeemed" widgetVar="nyugakGakkiNoDeemed" value="#{xgm00301T01Bean.condition.nyugakGakkiNoDeemed}"
                                                    styleClass="inputHalfSize2" 
                                                    >
                                        <p:clientValidator event="blur" />
                                    </rx:inputNumber>
                                    <p:message for="t1_nyugakGakkiNoDeemed:number" display="tooltip" />
                                </dd>
                            </div>
                            <div class="itemTable">
                                <!--カリキュラム学科組織-->
                                <dt><p:outputLabel value="#{item['common.cgks.0.label']}" /></dt>
                                <dd>
                                    <p:selectOneMenu id="t1_cgksCd" value="#{xgm00301T01Bean.condition.cgksCd}"
                                                     >
                                        <!--選択してください-->
                                        <f:selectItem itemLabel="#{item['common.select.sentaku.0.label']}" />
                                        <f:selectItems value="#{pkzSelectItem.getCgksItems(true)}" />
                                    </p:selectOneMenu>
                                </dd>
                            </div>
                        </p:outputPanel>
                        <div class="itemTable">
                            <!--学年-->
                            <dt><p:outputLabel value="#{item['common.gakunen.0.label']}" /></dt>
                            <dd>
                                <p:selectOneMenu id="t1_gaknen" value="#{xgm00301T01Bean.condition.gaknen}">
                                    <!--選択してください-->
                                    <f:selectItem itemLabel="#{item['common.select.sentaku.0.label']}" />
                                    <f:selectItems value="#{pkzSelectItem.gaknenItems}" />
                                </p:selectOneMenu>
                            </dd>
                            <!--セメスタ-->
                            <dt><p:outputLabel value="#{item['common.semester.0.label']}" /></dt>
                            <dd>
                                <p:selectOneMenu id="t1_semester" value="#{xgm00301T01Bean.condition.semester}"
                                                 >
                                    <!--選択してください-->
                                    <f:selectItem itemLabel="#{item['common.select.sentaku.0.label']}" />
                                    <f:selectItems value="#{pkzSelectItem.semesterItems}" />
                                </p:selectOneMenu>
                            </dd>
                        </div>
                        <p:outputPanel id="t1_centerArea">
                            <div class="itemTable">
                                <!--入学種別-->
                                <dt><p:outputLabel value="#{item['pkbGak.nyugakSbtCd.0.shortLabel']}" /></dt>
                                <dd>
                                    <p:selectOneMenu value="#{xgm00301T01Bean.condition.nyugakSbtCd}" id="t1_nyugakSbtCd"
                                                     >
                                        <!--選択してください-->
                                        <f:selectItem itemLabel="#{item['common.select.sentaku.0.label']}" />
                                        <f:selectItems value="#{pkzSelectItem.nygSbtItems}" />
                                    </p:selectOneMenu>
                                </dd>
                                <!--就学種別-->
                                <dt><p:outputLabel value="#{item['pkbGak.shugakSbtCd.0.shortLabel']}" /></dt>
                                <dd>
                                    <p:selectOneMenu value="#{xgm00301T01Bean.condition.shugakSbtCd}" id="t1_shugakSbtCd"
                                                     >
                                        <!--選択してください-->
                                        <f:selectItem itemLabel="#{item['common.select.sentaku.0.label']}" />
                                        <f:selectItems value="#{pkzSelectItem.shugaksbtItems}" />
                                    </p:selectOneMenu>
                                </dd>
                            </div>
                            <div class="itemTable">
                                <!--専攻コース種別-->
                                <dt><p:outputLabel value="#{item['pkbGakSnko.majorCourseSbtCd.0.shortLabel']}" /></dt>
                                <dd>
                                    <p:selectOneMenu id="t1_majorCourseSbtCd" value="#{xgm00301T01Bean.condition.majorCourseSbtCd}"
                                                     >
                                        <!--選択してください-->
                                        <f:selectItem itemLabel="#{item['common.select.sentaku.0.label']}" />
                                        <f:selectItems value="#{pkzSelectItem.snkoSbtItems}" />
                                        <p:ajax event="change" update="t1_majorCourseCd" />
                                    </p:selectOneMenu>
                                </dd>
                                <!--専攻コース-->
                                <dt><p:outputLabel value="#{item['pkbGakSnko.majorCourseCd.0.shortLabel']}" /></dt>
                                <dd>
                                    <p:selectOneMenu id="t1_majorCourseCd" value="#{xgm00301T01Bean.condition.majorCourseCd}"
                                                     >
                                        <!--選択してください-->
                                        <f:selectItem itemLabel="#{item['common.select.sentaku.0.label']}" />
                                        <f:selectItems value="#{pkzSelectItem.getSnkoItems(xgm00301T01Bean.condition.majorCourseSbtCd)}" />
                                    </p:selectOneMenu>
                                </dd>
                            </div>
                            <div class="itemTable">
                                <!--クラス種別-->
                                <dt><p:outputLabel value="#{item['pkbGakClass.classSbtCd.0.shortLabel']}" /></dt>
                                <dd>
                                    <p:selectOneMenu id="t1_classSbtCd" value="#{xgm00301T01Bean.condition.classSbtCd}"
                                                     >
                                        <!--選択してください-->
                                        <f:selectItem itemLabel="#{item['common.select.sentaku.0.label']}" />
                                        <f:selectItems value="#{pkzSelectItem.classSbtItems}" />
                                        <p:ajax event="change" update="t1_classCd" />
                                    </p:selectOneMenu>
                                </dd>
                                <!--クラス-->
                                <dt><p:outputLabel value="#{item['pkbGakClass.classCd.0.shortLabel']}" /></dt>
                                <dd>
                                    <p:selectOneMenu id="t1_classCd" value="#{xgm00301T01Bean.condition.classCd}"
                                                     >
                                        <!--選択してください-->
                                        <f:selectItem itemLabel="#{item['common.select.sentaku.0.label']}" />
                                        <f:selectItems value="#{pkzSelectItem.getClassItems(xgm00301T01Bean.condition.classSbtCd)}" />
                                    </p:selectOneMenu>
                                </dd>
                            </div>
                        </p:outputPanel>
                        <div class="itemTable">
                            <!-- 留学生対象 -->
                            <dt>
                                <p:outputLabel for="t1_ryugakseiFlg" value="#{item['ghd008.tgtRyugakusei.0.label']}" />
                                <rx:gkSizeLabel />
                            </dt>
                            <dd>
                                <p:selectOneMenu value="#{xgm00301T01Bean.condition.ryugakseiFlg}" id="t1_ryugakseiFlg">
                                    <!--選択してください-->
                                    <f:selectItem itemLabel="#{item['common.select.sentaku.0.label']}" />
                                    <f:selectItems value="#{ryugakseiFlg.itemValues}" var="itemVal"
                                                   itemLabel="#{ryugakseiFlg.getLabel(itemVal)}" itemValue="#{itemVal eq '1'}" />
                                </p:selectOneMenu>
                            </dd>
                            <!--留学生区分-->
                            <dt>
                                <p:outputLabel for="t1_ryugakseiKbn" value="#{item['pkbRgak.ryugakseiKbn.0.label']}" />
                            </dt>
                            <dd>
                                <p:selectOneMenu value="#{xgm00301T01Bean.condition.ryugakseiKbn}" id="t1_ryugakseiKbn"
                                                 >
                                    <!--選択してください-->
                                    <f:selectItem itemLabel="#{item['common.select.sentaku.0.label']}" />
                                    <f:selectItems value="#{ryugakseiKbn.itemValues}" var="itemVal"
                                                   itemLabel="#{ryugakseiKbn.getLabel(itemVal)}" itemValue="#{itemVal}" />
                                </p:selectOneMenu>
                            </dd>
                        </div>
                        <div class="itemTable">
                            <!--障害者対象-->
                            <dt>
                                <p:outputLabel for="t1_shogaisyaFlg" value="#{item['ghd008.tgtShogaisha.0.label']}" />
                            </dt>
                            <dd>
                                <p:selectOneMenu value="#{xgm00301T01Bean.condition.shogaisyaFlg}" id="t1_shogaisyaFlg">
                                    <!--選択してください-->
                                    <f:selectItem itemLabel="#{item['common.select.sentaku.0.label']}" />
                                    <f:selectItems value="#{shogaisyaFlg.itemValues}" var="itemVal"
                                                   itemLabel="#{shogaisyaFlg.getLabel(itemVal)}" itemValue="#{itemVal eq '1'}" />
                                </p:selectOneMenu>
                            </dd>
                            <!--社会人対象-->
                            <dt>
                                <p:outputLabel for="t1_shakaijinFlg" value="#{item['ghd008.tgtShakaijin.0.label']}" />
                            </dt>
                            <dd>
                                <p:selectOneMenu value="#{xgm00301T01Bean.condition.shakaijinFlg}" id="t1_shakaijinFlg">
                                    <!--選択してください-->
                                    <f:selectItem itemLabel="#{item['common.select.sentaku.0.label']}" />
                                    <f:selectItems value="#{shakaijinFlg.itemValues}" var="itemVal"
                                                   itemLabel="#{shakaijinFlg.getLabel(itemVal)}" itemValue="#{itemVal eq '1'}" />
                                </p:selectOneMenu>
                            </dd>
                        </div>
                        <p:outputPanel id="t1_frArea">
                            <div class="itemTable">
                                <!--学生自由設定項目-->
                                <dt><p:outputLabel value="#{item['common.pkbGkfrKomok.0.label']}"/></dt>
                                <dd>
                                    <p:commandButton value="#{btn['button.016']}"
                                                    styleClass="inTableBtn letterSpaceHalf"
                                                    action="#{xgm00301T01Bean.doClickPkGkfrCondition()}"
                                                    process="@this" 
                                                    />
                                    <h:outputText value="#{item['ghd008.jokenAri.0.label']}" rendered="#{not empty xgm00301T01Bean.condition.pkGkfrList}" />
                                </dd>
                                <!--学費自由設定項目-->
                                <dt>
                                    <p:outputLabel value="#{item['common.ghzGkfrKomok.0.label']}" />
                                </dt>
                                <dd>
                                    <p:commandButton value="#{btn['button.016']}"
                                                    styleClass="inTableBtn letterSpaceHalf"
                                                    action="#{xgm00301T01Bean.doClickGhGkfrCondition()}"
                                                    process="@this" 
                                                     />
                                    <h:outputText value="#{item['ghd008.jokenAri.0.label']}" rendered="#{not empty xgm00301T01Bean.condition.ghGkfrList}" />
                                </dd>
                            </div>
                        </p:outputPanel>
                        <p:outputPanel id="t1_bottomArea">
                            <div class="itemTable">
                                <!-- 出身校コード -->
                                <dt>
                                    <p:outputLabel for="t1_shshnkCd" value="#{item['common.pkaSsn.0.label']}" />
                                    <rx:gkSizeLabel />
                                </dt>
                                <dd>
                                    <p:commandButton id="t1_shshnkCdSearch" icon="fa fa-fw fa-search" actionListener="#{xgm00301T01Bean.doOpenSearchShshnk()}"
                                                     process="@this" styleClass="inTableBtnIconAlt"
                                                     /> 
                                    <p:inputText id="t1_shshnkCd" value="#{xgm00301T01Bean.condition.shshnkCd}" styleClass="inputHalfSize6"
                                                 >
                                        <p:clientValidator event="blur" />
                                        <p:ajax event="change" process="@this" update="t1_shshnkName" listener="#{xgm00301T01Bean.doGetShshnkName()}"/>
                                    </p:inputText>
                                    <p:message for="t1_shshnkCd" display="tooltip" />
                                    <h:outputText id="t1_shshnkName" value="#{xgm00301T01Bean.shshnkName}" styleClass="reflectName" />
                                </dd>
                            </div>                                
                            <div class="itemTable">
                                <!-- 異動期間 -->
                                <dt>
                                    <p:outputLabel for="funcForm:tabArea:t1_idoKikanFrom:t1_idoKikanFrom funcForm:tabArea:t1_idoKikanTo:t1_idoKikanTo"
                                                   value="#{item['common.yidoKikan.0.label']}" />
                                </dt>
                                <dd>
                                    <rx:calendar id="t1_idoKikanFrom" value="#{xgm00301T01Bean.condition.idoKikanFrom}" type="date" styleClass="inputDate"
                                                 
                                                 label="#{item['common.yidoKikanFrom.0.label']}"/>
                                    <h:outputText value="#{item['common.mark.wave.0.label']}" styleClass="fromtoMark"/>
                                    <rx:calendar id="t1_idoKikanTo" value="#{xgm00301T01Bean.condition.idoKikanTo}" type="date" styleClass="inputDate"
                                                 
                                                 label="#{item['common.yidoKikanTo.0.label']}"/>
                                </dd>
                                <!-- 異動出学申請結果 -->
                                <dt>
                                    <p:outputLabel value="#{item['pkbGakIdo.idoShutgakApplyKekkaKbn.0.shortLabel']}" />
                                    <rx:gkSizeLabel />
                                </dt>
                                <dd>
                                    <p:selectManyCheckbox id="t1_idoSin" value="#{xgm00301T01Bean.condition.idoShutgakApplyKekkaKbnList}"
                                                          >
                                        <f:selectItems value="#{idoSin.itemValues}" var="itemVal" itemLabel="#{idoSin.getLabel(itemVal)}" itemValue="#{itemVal}"/>				
                                    </p:selectManyCheckbox>
                                </dd>
                            </div>
                            <div class="itemTable">
                                <!-- 異動種別 -->
                                <dt>
                                    <p:outputLabel value="#{item['pkbIdoSbt.idoShutgakSbtCd.2.label']}" />
                                    <rx:gkSizeLabel />
                                </dt>
                                <dd>
                                    <rx:selectCheckboxMenu id="t1_idoSbt" items="#{pkzSelectItem.getIdoSbtItems('1')}"
                                                           value="#{xgm00301T01Bean.condition.idoShutgakSbtKbnList}"
                                                           />
                                </dd>
                            </div>
                        </p:outputPanel>
                    </dl>
                </p:outputPanel>
            </p:fieldset>
        </p:outputPanel>
    </section>
    <!-- 並び順指定エリア -->
    <rx:gkOrdDesignation id="t1_orderDetail" value="#{xgm00301T01Bean.t1_orderDetail}" selectItems="#{xgm00301Bean.orderItems}" maxPriorities="3" />

    <!--メインボタン-->
    <div class="btnAreaFuncBottom">
        <!--発行する-->
        <rx:asyncExecRequest id="t1_outputPrint" compoAsyncId="outputPrintBtn1"
                             process="@this funcForm:mainCondition funcForm:tabArea:t1_mainCondition @(.gkOrdDesignation)"
                             asyncExecReqDto="#{xgm00301T01Bean.collectiveOutputReport}"
                             update="@this funcForm:mainCondition funcForm:tabArea:t1_mainCondition @(.gkOrdDesignation)"
                             beforeMethod="#{xgm00301Bean.doOutputPrint(1)}"
                             buttonName="#{btn['button.802']}" buttonStyleClass="btnFileoutput letterSpaceHalf" />
    </div>
</ui:composition>