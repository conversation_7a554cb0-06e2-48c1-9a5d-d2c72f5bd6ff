/*
 * IGhd008Service.java
 *
 * 著作権  ：Copyright Japan System Techniques Co., Ltd. All Rights Reserved.
 * 会社名  ：日本システム技術株式会社
 *
 */
package com.jast.gakuen.gk.xg.service;

import java.util.List;

import javax.faces.model.SelectItem;

import com.jast.gakuen.gk.gh.dto.Ghd008DTO01;
import com.jast.gakuen.gk.gh.dto.Ghd008DTO05;
import com.jast.gakuen.gk.gh.dto.Ghd008DTO06;
import com.jast.gakuen.gk.gh.dto.Ghd008DTO12;
import com.jast.gakuen.gk.xg.dto.Xgm003ConditionDTO02;
import com.jast.gakuen.gk.xg.dto.Xgm003ConditionDTO04;
import com.jast.gakuen.gk.xg.dto.Xgm003DTO02;

/**
 * 学生納付金通知書サービスインターフェース
 *
 * <AUTHOR> System Techniques Co.,Ltd.
 */
public interface IXgm003Service {

	/**
	 * 並び順指定コンボ用リスト取得
	 *
	 * @return 並び順指定コンボリスト
	 * @throws Exception 例外
	 */
	List<Ghd008DTO01> getOrderList() throws Exception;

	/**
	 * 納付金配当情報取得
	 *
	 * @param condition 検索条件
	 * @return 納付金配当/割当情報取得戻り値DTOのリスト
	 * @throws Exception 例外
	 */
	List<Xgm003DTO02> getPayHList(final Xgm003ConditionDTO02 condition) throws Exception;

	/**
	 * 納付金割当情報取得
	 *
	 * @param condition 検索条件
	 * @return 納付金配当/割当情報取得戻り値DTOのリスト
	 * @throws Exception 例外
	 */
	List<Xgm003DTO02> getPayWList(final Xgm003ConditionDTO04 condition) throws Exception;

	/**
	 * 学籍番号チェック
	 *
	 * @param condition 検索条件
	 * @return 学生氏名DTO
	 * @throws Exception 例外
	 */
	Ghd008DTO05 checkGaksekiCd(final Ghd008DTO05 condition) throws Exception;

	/**
	 * 学生氏名取得
	 *
	 * @param condition 学生氏名DTO
	 * @return 学生氏名DTO
	 * @throws Exception 例外
	 */
	Ghd008DTO05 getGakseiName(final Ghd008DTO05 condition) throws Exception;

	/**
	 * 出身校情報取得
	 * 
	 * @param condition 検索条件
	 * @return 出身校情報
	 * @throws Exception 例外
	 */
	Ghd008DTO06 getShshnkName(final Ghd008DTO06 condition) throws Exception;

	/**
	 * 学費業務年度取得
	 * 
	 * @return 学費業務年度
	 * @throws Exception 例外
	 */
	Ghd008DTO12 getCurrentNendo() throws Exception;

	/**
	 * 学費文面を全件取得する。
	 *
	 * @return 学費文面リスト
	 * @throws Exception 例外
	 */
	List<SelectItem> getBunmenItems() throws Exception;

	/**
	 * EUC設定ファイルから「学費の納入期限範囲」の月数を取得
	 *
	 * @return 月数（デフォルト12）
	 * @throws Exception 例外
	 */
	Integer getGakuhiNoufuKigenRange() throws Exception;

}
