<?xml version='1.0' encoding='UTF-8' ?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.1//EN" "http://www.w3.org/TR/xhtml11/DTD/xhtml11.dtd">
<ui:composition xmlns="http://www.w3.org/1999/xhtml"
                xmlns:h="http://xmlns.jcp.org/jsf/html"
                xmlns:f="http://xmlns.jcp.org/jsf/core"
                xmlns:ui="http://xmlns.jcp.org/jsf/facelets"
                xmlns:p="http://primefaces.org/ui"
                xmlns:c="http://xmlns.jcp.org/jsp/jstl/core"
                xmlns:rx="http://xmlns.jcp.org/jsf/composite/component">

    <section>

        <p:outputPanel id="t2_paySearchCondition">
            <dl class="inputArea">
                <div class="itemTable">
                <!-- 割当年度 -->
                    <dt class="hissu">
                        <p:outputLabel for="funcForm:tabArea:t2_nendo:number" value="#{item['ghePaywBun.nendo.0.label']}" />
                        <rx:gkSizeLabel />
                    </dt>
                    <dd>
                        <rx:inputNumber id="t2_nendo" widgetVar="nendo" value="#{ghd00802T03Bean.condition.nendo}" styleClass="inputHalfSize4" >
                            <p:clientValidator event="blur" />
                        </rx:inputNumber>
                        <p:message for="funcForm:tabArea:t2_nendo:number" display="tooltip" />
                    </dd>
                </div>
                <div class="itemTable">
                    <dt>
                        <p:outputLabel for="t2_payCd" value="#{item['ghePaywBun.payCd.0.label']}" />
                        <rx:gkSizeLabel />
                    </dt>
                    <dd>
                        <p:inputText id="t2_payCd" value="#{ghd00802T03Bean.condition.payCd}" styleClass="inputHalfSize6" >
                            <p:clientValidator event="change" />
                        </p:inputText>
                        <p:message for="t2_payCd" display="tooltip" />
                        <!-- 検索方法 -->
                        <p:selectOneRadio id="t2_payCdSearchMethod" value="#{ghd00802T03Bean.condition.payCdSearchMethod}" >
                            <f:selectItem itemValue="2" itemLabel="#{item['common.same.before.0.label']}"/>
                            <f:selectItem itemValue="1" itemLabel="#{item['common.same.part.0.label']}"/>
                        </p:selectOneRadio>
                    </dd>
                </div>
                <div class="itemTable">
                    <dt>
                        <p:outputLabel for="t2_payName" value="#{item['ghcPayh.payName.0.label']}" />
                        <rx:gkSizeLabel />
                    </dt>
                    <dd>
                        <p:inputText id="t2_payName" value="#{ghd00802T03Bean.condition.payName}" styleClass="inputFullSize20" >
                            <p:clientValidator event="change" />
                        </p:inputText>
                        <p:message for="t2_payName" display="tooltip" />
                        <!-- 検索方法 -->
                        <p:selectOneRadio id="t2_payNameSearchMethod" value="#{ghd00802T03Bean.condition.payNameSearchMethod}" >
                            <f:selectItem itemValue="2" itemLabel="#{item['common.same.before.0.label']}"/>
                            <f:selectItem itemValue="1" itemLabel="#{item['common.same.part.0.label']}"/>
                        </p:selectOneRadio>
                    </dd>
                </div>
                <div class="itemTable">
                    <!--納入開始日-->
                    <dt>
                        <p:outputLabel for="funcForm:tabArea:t2_payStartDateFrom:t2_payStartDateFrom funcForm:tabArea:t2_payStartDateTo:t2_payStartDateTo"
                                       value="#{item['ghePaywBun.payStartDate.0.label']}" />
                    </dt>
                    <dd>                     
                        <rx:calendar id ="t2_payStartDateFrom" value="#{ghd00802T03Bean.condition.payStartDateFrom}" type="date"
                                      styleClass="inputDate" label="#{item['ghd008.payStartDateFrom.0.label']}"/>
                        <h:outputText value="#{item['common.mark.wave.0.label']}" styleClass="fromtoMark"/>
                        <rx:calendar id ="t2_payStartDateTo" value="#{ghd00802T03Bean.condition.payStartDateTo}" type="date"
                                      styleClass="inputDate" label="#{item['ghd008.payStartDateTo.0.label']}"/>
                    </dd>
                </div>
                <div class="itemTable">
                    <!--納入期限日-->
                    <dt>
                        <p:outputLabel for="funcForm:tabArea:t2_payLimitDateFrom:t2_payLimitDateFrom funcForm:tabArea:t2_payLimitDateTo:t2_payLimitDateTo"
                                       value="#{item['ghePaywBun.payLimitDate.0.label']}" />
                    </dt>
                    <dd>                     
                        <rx:calendar id ="t2_payLimitDateFrom" value="#{ghd00802T03Bean.condition.payLimitDateFrom}" type="date"
                                      styleClass="inputDate" label="#{item['ghd008.payLimitDateFrom.0.label']}"/>
                        <h:outputText value="#{item['common.mark.wave.0.label']}" styleClass="fromtoMark"/>
                        <rx:calendar id ="t2_payLimitDateTo" value="#{ghd00802T03Bean.condition.payLimitDateTo}" type="date"
                                      styleClass="inputDate" label="#{item['ghd008.payLimitDateTo.0.label']}"/>
                    </dd>
                </div>
            </dl>
        </p:outputPanel>
            
        <!-- ボタンエリア -->
        <p:outputPanel styleClass="btnAreaFuncBottom" >
            <!-- 検索 -->
            <p:commandButton id="t2_paySearch" value="#{btn['button.001']}" actionListener="#{ghd00802Bean.doSearchPayHList(3)}"
                             process="@this funcForm:mainCondition funcForm:outputKbnCondition t2_paySearchCondition"
                             update="@this t2_paySearchCondition funcForm:tabArea:t2_payList funcForm:tabArea:t2_paySearch funcForm:tabArea:t2_payClear"
                             styleClass="btnSearch letterSpaceHalf" />
            <!-- クリア -->
            <p:commandButton id="t2_payClear" value="#{btn['button.008']}" actionListener="#{ghd00802T03Bean.doClear}"
                             process="@this"
                             update="@this funcForm:tabArea:t2_paySearchCondition funcForm:tabArea:t2_payList funcForm:tabArea:t2_paySearch funcForm:tabArea:t2_payClear"
                             styleClass="btnClear"/>
        </p:outputPanel>
            
        <p:outputPanel id="t2_mainCondition">
            <dl>
                <div>
                    <!-- 納付金リスト -->
                    <p:dataTable id="#{ghd00802Bean.tblId[1]}"
                                 rows="#{ghd00802Bean.tblRows[1]}"
                                 first="#{ghd00802Bean.tblFirst[1]}"
                                 var="data" value="#{ghd00802T03Bean.payList}"
                                 selection="#{ghd00802T03Bean.selectedPayList}" rowKey="#{data.hashCode()}"
                                 widgetVar="dataTable" paginator="true" resizableColumns="false" scrollHeight="140"
                                 emptyMessage="#{ghd00802T03Bean.searched?msgSY['E_SY_00090']:''}">

                        <!--ページャ保存-->
                        <p:ajax event="page" listener="#{ghd00802Bean.onPage}" />

                        <!-- チェックボックス -->
                        <p:column selectionMode="multiple" class="colSizeCheckbox alignCenter" />

                        <!-- 割当年度 -->
                        <p:column id="t2_colNendo" headerText="#{item['ghePaywBun.nendo.0.label']}"
                                  sortBy="#{data.nendo}" styleClass="colSize6 alignLeft">
                            <h:outputText value="#{data.nendo}" />
                        </p:column>

                        <!-- 納付金コード -->
                        <p:column id="t2_colpayCd" headerText="#{item['ghePaywBun.payCd.0.label']}"
                                  sortBy="#{data.payCd}" styleClass="colSize8 alignLeft">
                            <h:outputText value="#{data.payCd}" />
                        </p:column>

                        <!-- パターンコード -->
                        <p:column id="t2_colPatternCd" headerText="#{item['ghePaywBun.patternCd.0.shortLabel']}"
                                  sortBy="#{data.patternCd}"
                                  styleClass="colSize9 alignLeft">
                            <h:outputText value="#{data.patternCd}" />
                        </p:column>

                        <!-- 分納区分コード -->
                        <p:column id="t2_colBunnoKbnCd" headerText="#{item['ghePaywBun.bunnoKbnCd.0.shortLabel']}"
                                  sortBy="#{data.bunnoKbnCd}"
                                  styleClass="colSize9 alignLeft">
                            <h:outputText value="#{ghd00802Bean.doJoinCodeName(data.bunnoKbnCd,data.bunnoKbnName)}" />
                        </p:column>

                        <!-- 納付金名称 -->
                        <p:column id="t2_colPayName" headerText="#{item['ghcPayh.payName.0.label']}"
                                  sortBy="#{data.payName}"
                                  styleClass="colSizeInitial alignLeft">
                            <h:outputText value="#{data.payName}" />
                        </p:column>

                        <!-- 分割NO -->
                        <p:column id="t2_colBunkatsuNo" headerText="#{item['ghePaywBun.bunkatsuNo.0.label']}"
                                  sortBy="#{data.bunkatsuNo}"
                                  styleClass="colSize6 alignRight">
                            <h:outputText value="#{data.bunkatsuNo}" />
                        </p:column>
                    </p:dataTable>
                </div>
            </dl>
            
            <!-- 学生個別指定エリア -->
            <rx:ghzIndvDesignationGaksei value="#{ghd00802T03Bean.targetGakseiList}" scrollHeight="280" checkDuplicate="true" businessFunc="#{ghd00802T03Bean.businessFunc}" sKijunDate="#{ghd00802T03Bean.conditionOutput.skijunDate}" checkKanriGai="true"/>
        </p:outputPanel>
    </section>

    <!--メインボタン-->
    <div class="btnAreaFuncBottom">
        <!--発行する-->
        <rx:asyncExecRequest id="t2_outputPrint" compoAsyncId="outputPrintBtn2"
                             process="@this funcForm:mainCondition funcForm:tabArea:t2_mainCondition @(.gkOrdDesignation)"
                             asyncExecReqDto="#{xgm00301T01Bean.collectiveOutputReport}"
                             update="@this funcForm:mainCondition funcForm:tabArea:t2_mainCondition @(.gkOrdDesignation)"
                             beforeMethod="#{xgm00301Bean.doOutputPrint(2)}"
                             buttonName="#{btn['button.802']}" buttonStyleClass="btnFileoutput letterSpaceHalf" />
    </div>
</ui:composition>