<?xml version='1.0' encoding='UTF-8' ?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.1//EN" "http://www.w3.org/TR/xhtml11/DTD/xhtml11.dtd">
<ui:composition xmlns="http://www.w3.org/1999/xhtml"
                xmlns:h="http://xmlns.jcp.org/jsf/html"
                xmlns:f="http://xmlns.jcp.org/jsf/core"
                xmlns:ui="http://xmlns.jcp.org/jsf/facelets"
                xmlns:p="http://primefaces.org/ui"
                xmlns:rx="http://xmlns.jcp.org/jsf/composite/component"
                xmlns:c="http://xmlns.jcp.org/jsp/jstl/core"
                template="/templates/frameComponent.xhtml">

    <ui:define name="metadata">
        <f:metadata>
            <f:event type="preRenderView" listener="#{xgm00301Bean.doPreRenderView}" />
        </f:metadata>
    </ui:define>

    <ui:define name="resource">
        <h:outputScript name="/gk/xg/xgm003/Xgm00301.js" target="body"/>
    </ui:define>

    <ui:define name="content">
        <!-- <p:remoteCommand name="updOutputItemsArea" actionListener="#{xgm00301Bean.updOutputItems}"
                         process="@this" update="outputItemsArea" /> -->
        <div class="content">
            <!-- フォーカス
            <p:focus for="funcForm" /> -->
            <!-- タブ共通エリア -->
            <section>
                <p:outputPanel id="mainCondition">
                    <dl class="inputArea">
                        <div class="itemTable">
                            <dt class="hissu">
                                <!--発行日-->
                                <p:outputLabel value="#{item['common.hakkoDate.0.label']}" />
                            </dt>
                            <dd>
                                <rx:calendar id ="hakkoDate" value="#{xgm00301Bean.condition.hattyuDate}" type="date"
                                       styleClass="inputDate" label="#{item['common.hakkoDate.0.label']}"/>
                            </dd>
                            <dt>
                                <!--通信区分-->
                                <p:outputLabel value="#{item['xgm003.tsuukyakKbn.0.label']}" />
                            </dt>
                            <dd>
                                <p:selectBooleanCheckbox value="#{xgm00301Bean.condition.tsuukyakKbn}"
                                                         itemLabel="#{item['xgm003.tsuukyakLabel.0.label']}">
                                    <p:ajax event="change" process="@this" update="tsuukyakArea" />
                                </p:selectBooleanCheckbox>
                            </dd>
                        </div>
                        <div class="itemTable">
                            <dt>
                                <!--出力区分-->
                                <p:outputLabel value="#{item['xgm003.outputKbn.0.label']}" />
                            </dt>
                            <dd>
                                <p:selectOneRadio id="outputKbn" value="#{xgm00301Bean.condition.outputKbn}" layout="pageDirection">
                                    <f:selectItem itemValue="1" itemLabel="#{item['xgm003.outputKbn1.0.label']}"/>
                                    <f:selectItem itemValue="2" itemLabel="#{item['xgm003.outputKbn2.0.label']}"/>
                                    <f:selectItem itemValue="3" itemLabel="#{item['xgm003.outputKbn3.0.label']}"/>
                                </p:selectOneRadio>
                            </dd>
                            <dt>
                                <!--通信欄(ペイジー)-->
                                <p:outputLabel value="#{item['xgm003.tsuukyak.0.label']}" />
                            </dt>
                            <dd>
                                <p:outputPanel id="tsuukyakArea">
                                    <p:inputTextarea value="#{xgm00301Bean.condition.tsuukyak}"
                                                     styleClass="inputFullSize30" rows="3"
                                                     disabled="#{xgm00301Bean.tsuukyakDisabled}" />
                                </p:outputPanel>
                            </dd>
                        </div>
                        <div class="itemTable">
                            <dt>
                                <!--期限区分-->
                                <p:outputLabel value="#{item['xgm003.limitKbn.0.label']}" />
                            </dt>
                            <dd>
                                <p:selectBooleanCheckbox value="#{xgm00301Bean.condition.limitKbn}"
                                                         itemLabel="#{item['xgm003.limitLabel.0.label']}">
                                    <p:ajax event="change" process="@this" update="limitArea" />
                                </p:selectBooleanCheckbox>
                            </dd>
                        </div>
                        <div class="itemTable">
                            <p:outputPanel id="limitArea">
                                <dt>
                                    <!--納入期限-->
                                    <p:outputLabel value="#{item['xgm003.payLimit.0.label']}" />
                                </dt>
                                <dd>
                                    <rx:calendar value="#{xgm00301Bean.condition.payLimit}" type="date"
                                           styleClass="inputDate" label="#{item['xgm003.payLimit.0.label']}"
                                           disabled="#{xgm00301Bean.payLimitDisabled}"/>
                                </dd>
                                <dt>
                                    <!--有効期限-->
                                    <p:outputLabel value="#{item['xgm003.yukouLimit.0.label']}" />
                                </dt>
                                <dd>
                                    <rx:calendar value="#{xgm00301Bean.condition.yukouLimit}" type="date"
                                           styleClass="inputDate" label="#{item['xgm003.yukouLimit.0.label']}"
                                           disabled="#{xgm00301Bean.yukouLimitDisabled}"/>
                                </dd>
                            </p:outputPanel>
                        </div>
                    </dl>
                </p:outputPanel>
            </section>
            <!-- タブエリア -->
            <p:tabView id="tabArea" widgetVar="tabArea" activeIndex="-1">

                <!-- 納付金指定タブ -->
                <p:tab title="#{tab['Xgm00301.1']}">
                    <ui:include src="/gk/xg/xgm003/Xgm00301T01.xhtml" />
                </p:tab>

                <!-- 納付金・学生指定タブ
                <p:tab title="#{tab['Xgm00301.2']}">
                    <ui:include src="/gk/xg/xgm003/Xgm00301T02.xhtml" />
                </p:tab> -->

                <!-- 学生指定タブ
                <p:tab title="#{tab['Xgm00301.3']}">
                    <ui:include src="/gk/xg/xgm003/Xgm00301T03.xhtml" />
                </p:tab> -->

            </p:tabView>
            <rx:gkTabActiveIndex target="tabArea" activeIndex="#{authBean.activeIndex}" />
        </div>
    </ui:define>
</ui:composition>
